<template>
	<view>
		
		<view class="display-a acc-top">
			<block v-for="(item,index) in tabs" :key="index">
				<view class="tabs" @click="getLabel(item.id)">
					<view class="margin-bottom_20rpx" :style="tabId == item.id ? 'color:#00EDFF;' : ''">{{item.name}}</view>
					<view class="tabs-line" :style="tabId == item.id ? 'background: #00EDFF;' : ''"></view>
				</view>
			</block>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a-js margin-bottom_30rpx">
						<view class="r-name" v-if="tabId == 2">{{item.answer+'#'+item.challenges}}</view>
						<view class="r-name" v-if="tabId == 1">{{item.name}}</view>
						<image @click="getDel(item.name,item.id)" class="img-73" :src="imgUrl + '73.png'"></image>
					</view>
					<view class="display-a">
						<block v-if="tabId == 1">
							<view class="color_CBCACA margin-right_40rpx">字数: {{item.words}}字</view>
						</block>
						<view class="color_CBCACA">{{item.create_time}}</view>
						<view class="display-a margin-left-auto" @click="detail(item.name,item.id)" v-if="tabId == 1">
							<view class="color_CBCACA">查看详情</view>
							<image class="img-58" :src="imgUrl + '58.png'"></image>
						</view>
					</view>
				</view>
			</block>
			
		</mescroll-body>
		
		<sunui-tabbar :fixed="true" :current="tabIndex" :types="3" tintColor="#A3C3FF" backgroundColor="#1B1B1B"></sunui-tabbar>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				tabIndex: 2,
				
				imgUrl: this.$imgUrl,
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				tabs: [
					{id:'1',name:'AI文案记录'},
					{id:'2',name:'AI标题记录'},
				],
				tabId: '1',
				
			}
		},
		
		onLoad() {
			
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 284;
				},
			})
			
		},
		
		onShow() {
			if (uni.getStorageSync('uid')) {
				let pagearr = getCurrentPages(); //获取应用页面栈
				let currentPage = pagearr[pagearr.length - 1]; //获取当前页面信息
				if (currentPage.options.type) {
					this.tabId = currentPage.options.type;
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}else {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
			
		},
		
		methods: {
			
			getLabel(type) {
				this.tabId = type;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			//查看详情
			detail(name, id) {
				uni.navigateTo({
					url: '/pages/index/AICreation/detail?name=' + name + '&id=' + id
				})
			},

			/*  删除  */
			getDel(name, id) {

				uni.showModal({
					title: '提示',
					content: this.tabId == 1 ? `确认删除${name}文案吗?` : '确认删除该标题吗?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.tabId == 1 ? this.$api.aiCreateDel : this.$api.delTitle,
								data: {
									uid: uni.getStorageSync('uid'),
									create_id: id
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},
			
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.tabId == 1 ? this.$api.aiCreateLog : this.$api.titleList,
					data: {
						uid: uni.getStorageSync('uid'),
						name: '',
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
				
		}
	}
</script>

<style lang="scss">
	
	.tabs-line {
		width: 95rpx;
		background-color: #080E1E;
		height: 6rpx;
		border-radius: 100rpx;
		margin-left: 140rpx;
	}
	
	.tabs {
		width: 375rpx;
		text-align: center;
		color: #E6E6E6;
	}
	
	.acc-top {
		padding: 24rpx 0;
		border-bottom: 1px solid rgb(73, 73, 73);
		margin-bottom: 20rpx;
	}
	
	.color_CBCACA {
		color: #CBCACA;
		font-size: 26rpx;
	}
	
	.r-name {
		font-size: 32rpx;
		width: 600rpx;
	}
	
	.img-58 {
		width: 28rpx;
		height: 28rpx;
		margin-left: 8rpx;
	}
	
	.img-73 {
		width: 34rpx;
		height: 34rpx;
	}
	
	.list-public {
		background-color: #111D37;
		padding: 24rpx 26rpx;
		color: #FFF;
	}
	
	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
	
</style>
