<template>
	<view class="a-top">
		<view class="display-fw-js">
			<view class="a-frame display-a" :style="{background: bgc}" v-for="item in list" :key="item.id"
				@click="getAdd(item)">
				<image class="img-25" :src="imgUrl + item.img"></image>
				<view>
					<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx"
						style="margin-bottom: 12rpx;">{{item.title}}
					</view>
					<view class="color_FFFFFF font-size_24rpx">{{item.desc || item.title}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				imgUrl: this.$imgUrl,
				list: [],
				bgc: ''
			}
		},
		methods: {
			getAdd(item) {
				switch (item.id) {
					case 1:
						uni.navigateTo({
							url: "/subPackages/subPackageA/report?type=1",
						});
						break;
					case 2:
						uni.navigateTo({
							url: "/subPackages/subPackageA/report2?type=1",
						});
						break;
					case 3:
						uni.navigateTo({
							url: "/subPackages/subPackageA/report2?type=2",
						});
						break;
					case 4:
						uni.navigateTo({
							url: "/subPackages/subPackageA/accountPackaging",
						});
						break;
					case 5:
						break;
					case 6:
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=2`,
						});
						break;
					case 7:
						uni.navigateTo({
							url: "/pages/index/hotspot/hotspot",
						});
						break;
					case 8:
						uni.navigateTo({
							url: "pages/index/selling/selling",
						});
						break;
					case 9:
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=15`,
						});
						break;
					case 10:
						uni.navigateTo({
							url: "/pages/index/clone/middle?type=2",
						});
						break;
					case 11:
						uni.navigateTo({
							url: "/pages/index/clone/clone",
						});
						break;
					case 12:
						uni.navigateTo({
							url: "/pages/index/synthesis/synthesis",
						});
						break;
					case 13:
						uni.navigateTo({
							url: "/pages/edit/edit",
						});
						break;
					case 14:
						uni.navigateTo({
							url: "/pages/edit/edit",
						});
						break;
					case 15:
						uni.navigateTo({
							url: "/pages/edit/edit",
						});
						break;
					default:
						break;
				}
			},
			setPagetitle(title) {
				uni.setNavigationBarTitle({
					title
				})
			}
		},
		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			if (options.title) {
				this.title = options.title
				this.setPagetitle(this.title)
			}
			if (this.type === 11) {
				this.list = [{
					id: 1,
					title: 'AI立项',
					img: 'h-1.png',
					desc: '智选赛道'
				}, {
					id: 2,
					title: 'AI诊断',
					img: 'h-2.png',
					desc: '精准洞察'
				}, {
					id: 3,
					title: 'AI商业定位',
					img: 'h-3.png',
					desc: '锚定价值'
				}, {
					id: 4,
					title: 'AI账号包装',
					img: 'h-4.png',
					desc: '形象外显'
				}]
			} else if (this.type === 12) {
				this.list = [{
					id: 5,
					title: '爆款选题',
					img: 'h-8.png',
					desc: '复刻爆款视频'
				}, {
					id: 6,
					title: '文案编导',
					img: 'h-6.png',
					desc: '自动生成脚本'
				}, {
					id: 7,
					title: '热点跟拍',
					img: 'h-9.png',
					desc: '实时追踪热点'
				}, {
					id: 8,
					title: '文案提取',
					img: 'h-5.png',
					desc: '形象外显'
				}, {
					id: 9,
					title: '小红书笔记',
					img: 'h-11.png',
					desc: '生成图文笔记'
				}]
				this.bgc = 'linear-gradient( 134deg, #5FB2FF 0%, #0050FC 100%)'
			} else if (this.type === 13) {
				this.list = [{
					id: 10,
					title: '声音克隆',
					img: 'h-12.png',
					desc: '仅需30秒声音复刻'
				}, {
					id: 11,
					title: '形象克隆',
					img: 'h-13.png',
					desc: '一分钟生成形象'
				}, {
					id: 12,
					title: '数字人',
					img: 'h-5.png',
					desc: '分身创建'
				}]
				this.bgc = 'linear-gradient(131deg, #fa8864 0%, #af4b19 100%)'
			} else if (this.type === 14) {
				this.list = [{
					id: 13,
					title: '口播精剪',
					img: 'h-7.png',
					desc: '复刻爆款视频'
				}, {
					id: 14,
					title: '批量混剪',
					img: 'h-14.png',
					desc: '只能合成视频'
				}, {
					id: 15,
					title: '爆款封面',
					img: 'h-16.png',
					desc: '实时追踪热点'
				}]
				this.bgc = 'linear-gradient( 134deg, #A65DFF 0%, #696EEC 100%)'
			}
		}
	}
</script>

<style lang="scss">
	.a-top {
		margin: 20rpx 0;
	}

	.display-fw-js {
		padding: 0 20rpx;
	}

	.a-frame {
		position: relative;
		width: 344rpx;
		padding: 30rpx;
		background: linear-gradient(131deg, #9CA0F4 0%, #2830DD 100%);
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		.img-25 {
			position: absolute;
			right: 6rpx;
			bottom: 6rpx;
			width: 80rpx;
			height: 80rpx;
		}
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>