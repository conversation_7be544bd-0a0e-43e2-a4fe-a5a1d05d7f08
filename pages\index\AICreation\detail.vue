<template>
	<view>
		<view class="display-a padding_30rpx color_FFFFFF">
			<view class="d-line"></view>
			<view class="font-size_32rpx">类型</view>
			<view class="font-size_26rpx margin-left_20rpx">
				(
				<block v-if="obj.type == 6">企业宣传</block>
				<block v-if="obj.type == 7">同城团购</block>
				<block v-if="obj.type == 8">电商带货</block>
				<block v-if="obj.type == 9">知识科普</block>
				<block v-if="obj.type == 10">情感专家</block>
				<block v-if="obj.type == 11">口播文案</block>
				<block v-if="obj.type == 12">朋友圈营销</block>
				<block v-if="obj.type == 13">小红书笔记</block>
				<block v-if="obj.type == 14">文案仿写</block>
				<block v-if="obj.type == 15">智能代写</block>
				<block v-if="obj.type == 16">视频提取</block>
				<block v-if="obj.type == 17">一键仿写</block>
				)
			</view>
			<view class="font-size_24rpx margin-left_20rpx" style="color: #CBCACA;">{{obj.words}}字</view>
		</view>
		
		<view class="d-text">
			<rich-parser :html="obj.answer" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load
				ref="article" selectable show-with-animation use-anchor>
				<!-- 加载中... -->
			</rich-parser>
		</view>
		
		<view style="height: 140rpx;"></view>
		
		<view class="but" @click="getCopy()">复制文案</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				id: '',
				
				obj: {},
				
			}
			
		},
		
		watch: {
			
		},
		
		onLoad(options) {
			if (options.name) {
				this.$sun.title(options.name);
			}
			if (options.id) {
				this.id = options.id;
				this.getDetail();
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			//复制
			getCopy() {
				if (this.obj.answer) {
					uni.setClipboardData({
						data: this.obj.answer,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
							// uni.showToast({
							// 	title: '复制成功',
							// 	icon: 'success',
							// 	duration: 4000
							// });
						},
						fail: (err) => {
							console.log("复制失败原因===>",err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：'+err,
								icon: 'none'
							});
						}
					});
				}
			},
			
			async getDetail() {
				const result = await this.$http.post({
					url: this.$api.aiCreateDetail,
					data: {
						uid: uni.getStorageSync('uid'),
						create_id: this.id
					}
				});
				if (result.errno == 0) {
					this.obj = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.but {
		position: fixed;
		bottom: 50rpx;
		left: 70rpx;
		width: 610rpx;
		text-align: center;
		border-radius: 100rpx;
		box-shadow: 2px 3px 14px 0px rgba(30, 156, 214, 0.81),inset 0px 0px 11px 0px rgba(204, 235, 255, 0.3);
		background: linear-gradient(90.00deg, rgb(105, 229, 253),rgb(68, 65, 253) 100%);
		color: #FFF;
		font-size: 32rpx;
		font-weight: 600;
		padding: 22rpx 0;
	}
	
	.d-text {
		width: 710rpx;
		background-color: #111D37;
		border-radius: 10rpx;
		margin: 0 20rpx;
		padding: 20rpx;
		color: #FFF;
	}
	
	.d-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245),rgb(66, 72, 244) 100%);
		margin-right: 20rpx;
	}
	
	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
	
</style>
